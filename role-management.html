<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色管理 - DETP</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            height: 100vh;
            display: flex;
        }

        /* 左侧导航栏 */
        .sidebar {
            width: 200px;
            background-color: #1e2a4a;
            color: white;
            display: flex;
            flex-direction: column;
        }

        .logo {
            padding: 20px;
            font-size: 18px;
            font-weight: bold;
            border-bottom: 1px solid #2d3a5a;
        }

        .nav-menu {
            flex: 1;
            padding: 0;
        }

        .nav-item {
            padding: 15px 20px;
            cursor: pointer;
            border-bottom: 1px solid #2d3a5a;
            transition: background-color 0.3s;
        }

        .nav-item:hover {
            background-color: #2d3a5a;
        }

        .nav-item.active {
            background-color: #4a90e2;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: white;
        }

        .header {
            padding: 20px 30px;
            border-bottom: 1px solid #e8e8e8;
            background-color: white;
        }

        .page-title {
            font-size: 20px;
            font-weight: 500;
            margin-bottom: 20px;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background-color: #4a90e2;
            color: white;
        }

        .btn-primary:hover {
            background-color: #357abd;
        }

        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background-color: #c0392b;
        }

        /* 表格区域 */
        .table-container {
            flex: 1;
            padding: 20px 30px;
            overflow-y: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .data-table th {
            background-color: #f8f9fa;
            padding: 12px 15px;
            text-align: left;
            font-weight: 500;
            border-bottom: 1px solid #e8e8e8;
            color: #333;
        }

        .data-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
            color: #666;
        }

        .data-table tr:hover {
            background-color: #f8f9fa;
        }

        /* 操作按钮 */
        .action-btn {
            padding: 4px 8px;
            margin: 0 2px;
            border: 1px solid;
            border-radius: 3px;
            font-size: 12px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }

        .btn-detail {
            color: #4a90e2;
            border-color: #4a90e2;
            background: white;
        }

        .btn-edit {
            color: #f39c12;
            border-color: #f39c12;
            background: white;
        }

        .btn-delete {
            color: #e74c3c;
            border-color: #e74c3c;
            background: white;
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 30px;
            border-top: 1px solid #e8e8e8;
            background-color: white;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .page-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 3px;
            font-size: 14px;
        }

        .page-btn:hover {
            background-color: #f5f5f5;
        }

        .page-btn.active {
            background-color: #4a90e2;
            color: white;
            border-color: #4a90e2;
        }
    </style>
</head>
<body>
    <!-- 左侧导航栏 -->
    <div class="sidebar">
        <div class="logo">🔷 DETP</div>
        <div class="nav-menu">
            <div class="nav-item">📊 数据管理</div>
            <div class="nav-item active">👥 角色管理</div>
            <div class="nav-item">🔧 API管理</div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 页面头部 -->
        <div class="header">
            <h1 class="page-title">角色管理</h1>
            <div class="action-buttons">
                <button class="btn btn-primary">+ 新增</button>
                <button class="btn btn-danger">批量删除</button>
            </div>
        </div>

        <!-- 表格内容 -->
        <div class="table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>角色名称</th>
                        <th>角色描述</th>
                        <th>创建时间 ↑</th>
                        <th>创建人</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>系统管理员</td>
                        <td>XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX</td>
                        <td>2025-06-01 16:45:34</td>
                        <td>丁文俊</td>
                        <td>
                            <a href="#" class="action-btn btn-detail">详情</a>
                            <a href="#" class="action-btn btn-edit">编辑</a>
                            <a href="#" class="action-btn btn-delete">删除</a>
                        </td>
                    </tr>
                    <tr>
                        <td>访客</td>
                        <td>XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX</td>
                        <td>2025-06-01 16:45:34</td>
                        <td>杨文</td>
                        <td>
                            <a href="#" class="action-btn btn-detail">详情</a>
                            <a href="#" class="action-btn btn-edit">编辑</a>
                            <a href="#" class="action-btn btn-delete">删除</a>
                        </td>
                    </tr>
                    <tr>
                        <td>平台用户</td>
                        <td>XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX</td>
                        <td>2025-06-01 16:45:34</td>
                        <td>张明</td>
                        <td>
                            <a href="#" class="action-btn btn-detail">详情</a>
                            <a href="#" class="action-btn btn-edit">编辑</a>
                            <a href="#" class="action-btn btn-delete">删除</a>
                        </td>
                    </tr>
                    <tr>
                        <td>白名单用户</td>
                        <td>XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX</td>
                        <td>2025-06-01 16:45:34</td>
                        <td>丁文</td>
                        <td>
                            <a href="#" class="action-btn btn-detail">详情</a>
                            <a href="#" class="action-btn btn-edit">编辑</a>
                            <a href="#" class="action-btn btn-delete">删除</a>
                        </td>
                    </tr>
                    <tr>
                        <td>白名单用户</td>
                        <td>XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX</td>
                        <td>2025-06-01 16:45:34</td>
                        <td>丁文</td>
                        <td>
                            <a href="#" class="action-btn btn-detail">详情</a>
                            <a href="#" class="action-btn btn-edit">编辑</a>
                            <a href="#" class="action-btn btn-delete">删除</a>
                        </td>
                    </tr>
                    <tr>
                        <td>白名单用户</td>
                        <td>XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX</td>
                        <td>2025-06-01 16:45:34</td>
                        <td>丁文</td>
                        <td>
                            <a href="#" class="action-btn btn-detail">详情</a>
                            <a href="#" class="action-btn btn-edit">编辑</a>
                            <a href="#" class="action-btn btn-delete">删除</a>
                        </td>
                    </tr>
                    <tr>
                        <td>白名单用户</td>
                        <td>XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX</td>
                        <td>2025-06-01 16:45:34</td>
                        <td>丁文</td>
                        <td>
                            <a href="#" class="action-btn btn-detail">详情</a>
                            <a href="#" class="action-btn btn-edit">编辑</a>
                            <a href="#" class="action-btn btn-delete">删除</a>
                        </td>
                    </tr>
                    <tr>
                        <td>白名单用户</td>
                        <td>XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX</td>
                        <td>2025-06-01 16:45:34</td>
                        <td>丁文</td>
                        <td>
                            <a href="#" class="action-btn btn-detail">详情</a>
                            <a href="#" class="action-btn btn-edit">编辑</a>
                            <a href="#" class="action-btn btn-delete">删除</a>
                        </td>
                    </tr>
                    <tr>
                        <td>白名单用户</td>
                        <td>XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX</td>
                        <td>2025-06-01 16:45:34</td>
                        <td>丁文</td>
                        <td>
                            <a href="#" class="action-btn btn-detail">详情</a>
                            <a href="#" class="action-btn btn-edit">编辑</a>
                            <a href="#" class="action-btn btn-delete">删除</a>
                        </td>
                    </tr>
                    <tr>
                        <td>白名单用户</td>
                        <td>XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX</td>
                        <td>2025-06-01 16:45:34</td>
                        <td>丁文</td>
                        <td>
                            <a href="#" class="action-btn btn-detail">详情</a>
                            <a href="#" class="action-btn btn-edit">编辑</a>
                            <a href="#" class="action-btn btn-delete">删除</a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分页控件 -->
        <div class="pagination">
            <div class="pagination-info">共 35 条，每页 10 条</div>
            <div class="pagination-controls">
                <button class="page-btn">‹</button>
                <button class="page-btn active">1</button>
                <button class="page-btn">2</button>
                <button class="page-btn">3</button>
                <span>...</span>
                <button class="page-btn">30</button>
                <button class="page-btn">›</button>
                <button class="page-btn">尾页</button>
            </div>
        </div>
    </div>
</body>
</html>
